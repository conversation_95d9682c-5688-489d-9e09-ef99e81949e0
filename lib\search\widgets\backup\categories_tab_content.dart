import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:portraitmode/app/config/colors.dart';
import 'package:portraitmode/auth/utils/auth_util.dart';
import 'package:portraitmode/category/dto/category_data.dart';
import 'package:portraitmode/category/http_responses/category_list_response.dart';
import 'package:portraitmode/category/services/category_list_service.dart';
import 'package:portraitmode/category/widgets/category_list_item.dart';
import 'package:portraitmode/dialogs/error_dialog.dart';
import 'package:portraitmode/load_more/enum.dart';
import 'package:portraitmode/load_more/sliver_grid_load_more.dart';
import 'package:portraitmode/search/dto/search_data.dart';
import 'package:portraitmode/search/providers/search_categories_data_provider.dart';
import 'package:portraitmode/search/providers/search_category_list_provider.dart';

class CategoriesTabContent extends ConsumerStatefulWidget {
  const CategoriesTabContent({
    super.key,
    required this.searchData,
    required this.keyword,
    required this.dataList,
  });

  final CategoriesSearchData searchData;
  final String keyword;
  final List<CategoryData> dataList;

  @override
  CategoriesTabContentState createState() => CategoriesTabContentState();
}

class CategoriesTabContentState extends ConsumerState<CategoriesTabContent> {
  final _refreshIndicatorKey = GlobalKey<RefreshIndicatorState>();

  final _categoryListService = CategoryListService();

  // The other tabs are using 20 as the value,
  // but we're showing 2 columns per row for categories search results.
  // So 22 will make it even / well-paired.
  final int _loadMorePerPage = 22;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Center(
        child: Container(
          constraints: const BoxConstraints(maxWidth: 768.0),
          child: RefreshIndicator(
            key: _refreshIndicatorKey,
            onRefresh: _handleRefresh,
            child: SliverGridLoadMore(
              isFinished: widget.searchData.loadMoreEndReached,
              onLoadMore: _handleLoadMore,
              loadingWidgetColor: context.colors.baseColorAlt,
              runOnEmptyResult: true,
              loadingStatusText: "",
              finishedStatusText: "",
              verticalPaddingValue: 8.0,
              horizontalPaddingValue: 8.0,
              crossAxisCount: 2,
              mainAxisSpacing: 8.0,
              crossAxisSpacing: 8.0,
              children: _generateGridItems(),
            ),
          ),
        ),
      ),
    );
  }

  List<Widget> _generateGridItems() {
    return [
      for (CategoryData category in widget.dataList)
        CategoryListItem(category: category),
    ];
  }

  Future<void> _handleRefresh() async {
    late CategoryListResponse response;

    // log('The categories search keyword onRefresh is: ${widget.keyword}');

    if (widget.keyword.isNotEmpty) {
      response = await _categoryListService.search(
        keyword: widget.keyword,
        limit: _loadMorePerPage,
        offset: 0,
        hideEmpty: true,
      );
    } else {
      response = await _categoryListService.fetch(
        limit: _loadMorePerPage,
        offset: 0,
        hideEmpty: true,
      );
    }

    _handleCategoriesResponse(response, true, false);
  }

  Future<LoadMoreResult> _handleLoadMore() async {
    late CategoryListResponse response;

    // log('The categories search keyword is: "${widget.keyword}"');

    if (widget.keyword.isNotEmpty) {
      response = await _categoryListService.search(
        keyword: widget.keyword,
        limit: _loadMorePerPage,
        offset: widget.searchData.offset,
        hideEmpty: true,
      );
    } else {
      response = await _categoryListService.fetch(
        limit: _loadMorePerPage,
        offset: widget.searchData.offset,
        hideEmpty: true,
      );
    }

    final isFirstLoad = widget.searchData.offset == 0;

    _handleCategoriesResponse(response, false, isFirstLoad);

    if (!response.success) {
      return LoadMoreResult.failed;
    }

    if (response.data.isEmpty || response.data.length < _loadMorePerPage) {
      return LoadMoreResult.finished;
    }

    return LoadMoreResult.success;
  }

  void _handleCategoriesResponse(
    CategoryListResponse response,
    bool isRefresh,
    bool isFirstLoad,
  ) {
    if (!response.success) {
      if (authUtil.errorCodeRequiresLogin(response.errorCode)) {
        showSessionEndedDialog(context, ref);
      } else {
        showErrorDialog(context, ref, message: response.message);
      }

      return;
    }

    if (response.data.isEmpty) {
      ref
          .read(searchCategoriesDataProvider.notifier)
          .setLoadMoreEndReached(true);

      return;
    }

    final currentOffset = isRefresh
        ? 0
        : ref.read(searchCategoriesDataProvider).offset;

    ref
        .read(searchCategoriesDataProvider.notifier)
        .setOffset(currentOffset + response.data.length);

    if (isRefresh) {
      ref.read(searchCategoryListProvider.notifier).replaceAll(response.data);
    } else {
      if (isFirstLoad) {
        ref.read(searchCategoryListProvider.notifier).replaceAll(response.data);
      } else {
        ref.read(searchCategoryListProvider.notifier).addItems(response.data);
      }
    }

    if (response.data.length < _loadMorePerPage) {
      ref
          .read(searchCategoriesDataProvider.notifier)
          .setLoadMoreEndReached(true);
    } else {
      ref
          .read(searchCategoriesDataProvider.notifier)
          .setLoadMoreEndReached(false);
    }
  }
}
